const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Index-DIxC5n1W.js","assets/js/ui-vendor-WY5xTeZk.js","assets/js/react-vendor-o6ozJo2K.js","assets/js/utils-vendor--BulIq_u.js","assets/js/icons-vendor-B9WvW-j2.js","assets/js/query-vendor--kaXPEoe.js","assets/js/router-vendor-CClBJTgV.js","assets/js/suz-analytics-zHNAtlB7.js","assets/js/analytics-vendor-vOw2-8uu.js","assets/js/suz-seo-DhXwCDbS.js","assets/js/NotFound-Cpc_r-E2.js","assets/js/CookieConsent-BGXdIGSL.js"])))=>i.map(i=>d[i]);
import{j as c,V as Ge,R as Qe,A as Xe,C as Ze,T as et,D as tt,P as St,a as at,b as Ct}from"./ui-vendor-WY5xTeZk.js";import{a as Nt,r as E,o as n,v as Pt}from"./react-vendor-o6ozJo2K.js";import{t as jt,c as kt,a as It}from"./utils-vendor--BulIq_u.js";import{X as Mt}from"./icons-vendor-B9WvW-j2.js";import{Q as Rt,a as At}from"./query-vendor--kaXPEoe.js";import{B as Ot,R as zt,a as $e,u as Lt}from"./router-vendor-CClBJTgV.js";import{i as Bt,p as Ve,t as Dt}from"./suz-analytics-zHNAtlB7.js";import{u as Ye,i as _t,g as Ft}from"./suz-seo-DhXwCDbS.js";import"./analytics-vendor-vOw2-8uu.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const u of i.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&r(u)}).observe(document,{childList:!0,subtree:!0});function a(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=a(s);fetch(s.href,i)}})();var rt,He=Nt;rt=He.createRoot,He.hydrateRoot;const $t="modulepreload",Vt=function(t){return"/"+t},We={},Ie=function(e,a,r){let s=Promise.resolve();if(a&&a.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),m=u?.nonce||u?.getAttribute("nonce");s=Promise.allSettled(a.map(d=>{if(d=Vt(d),d in We)return;We[d]=!0;const h=d.endsWith(".css"),w=h?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${w}`))return;const N=document.createElement("link");if(N.rel=h?"stylesheet":$t,h||(N.as="script"),N.crossOrigin="",N.href=d,m&&N.setAttribute("nonce",m),document.head.appendChild(N),h)return new Promise((Q,o)=>{N.addEventListener("load",Q),N.addEventListener("error",()=>o(new Error(`Unable to preload CSS for ${d}`)))})}))}function i(u){const m=new Event("vite:preloadError",{cancelable:!0});if(m.payload=u,window.dispatchEvent(m),!m.defaultPrevented)throw u}return s.then(u=>{for(const m of u||[])m.status==="rejected"&&i(m.reason);return e().catch(i)})},Yt=1,Ht=1e6;let Pe=0;function Wt(){return Pe=(Pe+1)%Number.MAX_SAFE_INTEGER,Pe.toString()}const je=new Map,Ue=t=>{if(je.has(t))return;const e=setTimeout(()=>{je.delete(t),oe({type:"REMOVE_TOAST",toastId:t})},Ht);je.set(t,e)},Ut=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,Yt)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(a=>a.id===e.toast.id?{...a,...e.toast}:a)};case"DISMISS_TOAST":{const{toastId:a}=e;return a?Ue(a):t.toasts.forEach(r=>{Ue(r.id)}),{...t,toasts:t.toasts.map(r=>r.id===a||a===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(a=>a.id!==e.toastId)}}},ge=[];let ve={toasts:[]};function oe(t){ve=Ut(ve,t),ge.forEach(e=>{e(ve)})}function Kt({...t}){const e=Wt(),a=s=>oe({type:"UPDATE_TOAST",toast:{...s,id:e}}),r=()=>oe({type:"DISMISS_TOAST",toastId:e});return oe({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:s=>{s||r()}}}),{id:e,dismiss:r,update:a}}function qt(){const[t,e]=E.useState(ve);return E.useEffect(()=>(ge.push(e),()=>{const a=ge.indexOf(e);a>-1&&ge.splice(a,1)}),[t]),{...t,toast:Kt,dismiss:a=>oe({type:"DISMISS_TOAST",toastId:a})}}function H(...t){return jt(kt(t))}const Jt=St,ot=E.forwardRef(({className:t,...e},a)=>c.jsx(Ge,{ref:a,className:H("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...e}));ot.displayName=Ge.displayName;const Gt=It("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),st=E.forwardRef(({className:t,variant:e,...a},r)=>c.jsx(Qe,{ref:r,className:H(Gt({variant:e}),t),...a}));st.displayName=Qe.displayName;const Qt=E.forwardRef(({className:t,...e},a)=>c.jsx(Xe,{ref:a,className:H("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...e}));Qt.displayName=Xe.displayName;const nt=E.forwardRef(({className:t,...e},a)=>c.jsx(Ze,{ref:a,className:H("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...e,children:c.jsx(Mt,{className:"h-4 w-4"})}));nt.displayName=Ze.displayName;const it=E.forwardRef(({className:t,...e},a)=>c.jsx(et,{ref:a,className:H("text-sm font-semibold",t),...e}));it.displayName=et.displayName;const lt=E.forwardRef(({className:t,...e},a)=>c.jsx(tt,{ref:a,className:H("text-sm opacity-90",t),...e}));lt.displayName=tt.displayName;function Xt(){const{toasts:t}=qt();return c.jsxs(Jt,{children:[t.map(function({id:e,title:a,description:r,action:s,...i}){return c.jsxs(st,{...i,children:[c.jsxs("div",{className:"grid gap-1",children:[a&&c.jsx(it,{children:a}),r&&c.jsx(lt,{children:r})]}),s,c.jsx(nt,{})]},e)}),c.jsx(ot,{})]})}var Zt=t=>{switch(t){case"success":return aa;case"info":return oa;case"warning":return ra;case"error":return sa;default:return null}},ea=Array(12).fill(0),ta=({visible:t,className:e})=>n.createElement("div",{className:["sonner-loading-wrapper",e].filter(Boolean).join(" "),"data-visible":t},n.createElement("div",{className:"sonner-spinner"},ea.map((a,r)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),aa=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),ra=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),oa=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),sa=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),na=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),ia=()=>{let[t,e]=n.useState(document.hidden);return n.useEffect(()=>{let a=()=>{e(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),t},ke=1,la=class{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach(e=>e(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:a,...r}=t,s=typeof t?.id=="number"||((e=t.id)==null?void 0:e.length)>0?t.id:ke++,i=this.toasts.find(m=>m.id===s),u=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(s)&&this.dismissedToasts.delete(s),i?this.toasts=this.toasts.map(m=>m.id===s?(this.publish({...m,...t,id:s,title:a}),{...m,...t,id:s,dismissible:u,title:a}):m):this.addToast({title:a,...r,dismissible:u,id:s}),s},this.dismiss=t=>(this.dismissedToasts.add(t),t||this.toasts.forEach(e=>{this.subscribers.forEach(a=>a({id:e.id,dismiss:!0}))}),this.subscribers.forEach(e=>e({id:t,dismiss:!0})),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(t,e)=>{if(!e)return;let a;e.loading!==void 0&&(a=this.create({...e,promise:t,type:"loading",message:e.loading,description:typeof e.description!="function"?e.description:void 0}));let r=t instanceof Promise?t:t(),s=a!==void 0,i,u=r.then(async d=>{if(i=["resolve",d],n.isValidElement(d))s=!1,this.create({id:a,type:"default",message:d});else if(da(d)&&!d.ok){s=!1;let h=typeof e.error=="function"?await e.error(`HTTP error! status: ${d.status}`):e.error,w=typeof e.description=="function"?await e.description(`HTTP error! status: ${d.status}`):e.description;this.create({id:a,type:"error",message:h,description:w})}else if(e.success!==void 0){s=!1;let h=typeof e.success=="function"?await e.success(d):e.success,w=typeof e.description=="function"?await e.description(d):e.description;this.create({id:a,type:"success",message:h,description:w})}}).catch(async d=>{if(i=["reject",d],e.error!==void 0){s=!1;let h=typeof e.error=="function"?await e.error(d):e.error,w=typeof e.description=="function"?await e.description(d):e.description;this.create({id:a,type:"error",message:h,description:w})}}).finally(()=>{var d;s&&(this.dismiss(a),a=void 0),(d=e.finally)==null||d.call(e)}),m=()=>new Promise((d,h)=>u.then(()=>i[0]==="reject"?h(i[1]):d(i[1])).catch(h));return typeof a!="string"&&typeof a!="number"?{unwrap:m}:Object.assign(a,{unwrap:m})},this.custom=(t,e)=>{let a=e?.id||ke++;return this.create({jsx:t(a),id:a,...e}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},C=new la,ca=(t,e)=>{let a=e?.id||ke++;return C.addToast({title:t,...e,id:a}),a},da=t=>t&&typeof t=="object"&&"ok"in t&&typeof t.ok=="boolean"&&"status"in t&&typeof t.status=="number",ua=ca,ma=()=>C.toasts,fa=()=>C.getActiveToasts();Object.assign(ua,{success:C.success,info:C.info,warning:C.warning,error:C.error,custom:C.custom,message:C.message,promise:C.promise,dismiss:C.dismiss,loading:C.loading},{getHistory:ma,getToasts:fa});function ha(t,{insertAt:e}={}){if(typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",e==="top"&&a.firstChild?a.insertBefore(r,a.firstChild):a.appendChild(r),r.styleSheet?r.styleSheet.cssText=t:r.appendChild(document.createTextNode(t))}ha(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function he(t){return t.label!==void 0}var pa=3,ga="32px",va="16px",Ke=4e3,ya=356,ba=14,wa=20,xa=200;function A(...t){return t.filter(Boolean).join(" ")}function Ea(t){let[e,a]=t.split("-"),r=[];return e&&r.push(e),a&&r.push(a),r}var Ta=t=>{var e,a,r,s,i,u,m,d,h,w,N;let{invert:Q,toast:o,unstyled:ye,interacting:T,setHeights:O,visibleToasts:se,heights:W,index:B,toasts:be,expanded:X,removeToast:k,defaultRichColors:Z,closeButton:ne,style:ie,cancelButtonStyle:we,actionButtonStyle:le,className:z="",descriptionClassName:ce="",duration:U,position:de,gap:$,loadingIcon:L,expandByDefault:ue,classNames:f,icons:P,closeButtonAriaLabel:xe="Close toast",pauseWhenPageIsHidden:g}=t,[v,b]=n.useState(null),[S,K]=n.useState(null),[x,Ee]=n.useState(!1),[ee,me]=n.useState(!1),[te,Te]=n.useState(!1),[Ae,ut]=n.useState(!1),[mt,Oe]=n.useState(!1),[ft,Se]=n.useState(0),[ht,ze]=n.useState(0),ae=n.useRef(o.duration||U||Ke),Le=n.useRef(null),V=n.useRef(null),pt=B===0,gt=B+1<=se,j=o.type,q=o.dismissible!==!1,vt=o.className||"",yt=o.descriptionClassName||"",fe=n.useMemo(()=>W.findIndex(l=>l.toastId===o.id)||0,[W,o.id]),bt=n.useMemo(()=>{var l;return(l=o.closeButton)!=null?l:ne},[o.closeButton,ne]),Be=n.useMemo(()=>o.duration||U||Ke,[o.duration,U]),Ce=n.useRef(0),J=n.useRef(0),De=n.useRef(0),G=n.useRef(null),[wt,xt]=de.split("-"),_e=n.useMemo(()=>W.reduce((l,p,y)=>y>=fe?l:l+p.height,0),[W,fe]),Fe=ia(),Et=o.invert||Q,Ne=j==="loading";J.current=n.useMemo(()=>fe*$+_e,[fe,_e]),n.useEffect(()=>{ae.current=Be},[Be]),n.useEffect(()=>{Ee(!0)},[]),n.useEffect(()=>{let l=V.current;if(l){let p=l.getBoundingClientRect().height;return ze(p),O(y=>[{toastId:o.id,height:p,position:o.position},...y]),()=>O(y=>y.filter(I=>I.toastId!==o.id))}},[O,o.id]),n.useLayoutEffect(()=>{if(!x)return;let l=V.current,p=l.style.height;l.style.height="auto";let y=l.getBoundingClientRect().height;l.style.height=p,ze(y),O(I=>I.find(M=>M.toastId===o.id)?I.map(M=>M.toastId===o.id?{...M,height:y}:M):[{toastId:o.id,height:y,position:o.position},...I])},[x,o.title,o.description,O,o.id]);let D=n.useCallback(()=>{me(!0),Se(J.current),O(l=>l.filter(p=>p.toastId!==o.id)),setTimeout(()=>{k(o)},xa)},[o,k,O,J]);n.useEffect(()=>{if(o.promise&&j==="loading"||o.duration===1/0||o.type==="loading")return;let l;return X||T||g&&Fe?(()=>{if(De.current<Ce.current){let p=new Date().getTime()-Ce.current;ae.current=ae.current-p}De.current=new Date().getTime()})():ae.current!==1/0&&(Ce.current=new Date().getTime(),l=setTimeout(()=>{var p;(p=o.onAutoClose)==null||p.call(o,o),D()},ae.current)),()=>clearTimeout(l)},[X,T,o,j,g,Fe,D]),n.useEffect(()=>{o.delete&&D()},[D,o.delete]);function Tt(){var l,p,y;return P!=null&&P.loading?n.createElement("div",{className:A(f?.loader,(l=o?.classNames)==null?void 0:l.loader,"sonner-loader"),"data-visible":j==="loading"},P.loading):L?n.createElement("div",{className:A(f?.loader,(p=o?.classNames)==null?void 0:p.loader,"sonner-loader"),"data-visible":j==="loading"},L):n.createElement(ta,{className:A(f?.loader,(y=o?.classNames)==null?void 0:y.loader),visible:j==="loading"})}return n.createElement("li",{tabIndex:0,ref:V,className:A(z,vt,f?.toast,(e=o?.classNames)==null?void 0:e.toast,f?.default,f?.[j],(a=o?.classNames)==null?void 0:a[j]),"data-sonner-toast":"","data-rich-colors":(r=o.richColors)!=null?r:Z,"data-styled":!(o.jsx||o.unstyled||ye),"data-mounted":x,"data-promise":!!o.promise,"data-swiped":mt,"data-removed":ee,"data-visible":gt,"data-y-position":wt,"data-x-position":xt,"data-index":B,"data-front":pt,"data-swiping":te,"data-dismissible":q,"data-type":j,"data-invert":Et,"data-swipe-out":Ae,"data-swipe-direction":S,"data-expanded":!!(X||ue&&x),style:{"--index":B,"--toasts-before":B,"--z-index":be.length-B,"--offset":`${ee?ft:J.current}px`,"--initial-height":ue?"auto":`${ht}px`,...ie,...o.style},onDragEnd:()=>{Te(!1),b(null),G.current=null},onPointerDown:l=>{Ne||!q||(Le.current=new Date,Se(J.current),l.target.setPointerCapture(l.pointerId),l.target.tagName!=="BUTTON"&&(Te(!0),G.current={x:l.clientX,y:l.clientY}))},onPointerUp:()=>{var l,p,y,I;if(Ae||!q)return;G.current=null;let M=Number(((l=V.current)==null?void 0:l.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),_=Number(((p=V.current)==null?void 0:p.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Y=new Date().getTime()-((y=Le.current)==null?void 0:y.getTime()),R=v==="x"?M:_,F=Math.abs(R)/Y;if(Math.abs(R)>=wa||F>.11){Se(J.current),(I=o.onDismiss)==null||I.call(o,o),K(v==="x"?M>0?"right":"left":_>0?"down":"up"),D(),ut(!0),Oe(!1);return}Te(!1),b(null)},onPointerMove:l=>{var p,y,I,M;if(!G.current||!q||((p=window.getSelection())==null?void 0:p.toString().length)>0)return;let _=l.clientY-G.current.y,Y=l.clientX-G.current.x,R=(y=t.swipeDirections)!=null?y:Ea(de);!v&&(Math.abs(Y)>1||Math.abs(_)>1)&&b(Math.abs(Y)>Math.abs(_)?"x":"y");let F={x:0,y:0};v==="y"?(R.includes("top")||R.includes("bottom"))&&(R.includes("top")&&_<0||R.includes("bottom")&&_>0)&&(F.y=_):v==="x"&&(R.includes("left")||R.includes("right"))&&(R.includes("left")&&Y<0||R.includes("right")&&Y>0)&&(F.x=Y),(Math.abs(F.x)>0||Math.abs(F.y)>0)&&Oe(!0),(I=V.current)==null||I.style.setProperty("--swipe-amount-x",`${F.x}px`),(M=V.current)==null||M.style.setProperty("--swipe-amount-y",`${F.y}px`)}},bt&&!o.jsx?n.createElement("button",{"aria-label":xe,"data-disabled":Ne,"data-close-button":!0,onClick:Ne||!q?()=>{}:()=>{var l;D(),(l=o.onDismiss)==null||l.call(o,o)},className:A(f?.closeButton,(s=o?.classNames)==null?void 0:s.closeButton)},(i=P?.close)!=null?i:na):null,o.jsx||E.isValidElement(o.title)?o.jsx?o.jsx:typeof o.title=="function"?o.title():o.title:n.createElement(n.Fragment,null,j||o.icon||o.promise?n.createElement("div",{"data-icon":"",className:A(f?.icon,(u=o?.classNames)==null?void 0:u.icon)},o.promise||o.type==="loading"&&!o.icon?o.icon||Tt():null,o.type!=="loading"?o.icon||P?.[j]||Zt(j):null):null,n.createElement("div",{"data-content":"",className:A(f?.content,(m=o?.classNames)==null?void 0:m.content)},n.createElement("div",{"data-title":"",className:A(f?.title,(d=o?.classNames)==null?void 0:d.title)},typeof o.title=="function"?o.title():o.title),o.description?n.createElement("div",{"data-description":"",className:A(ce,yt,f?.description,(h=o?.classNames)==null?void 0:h.description)},typeof o.description=="function"?o.description():o.description):null),E.isValidElement(o.cancel)?o.cancel:o.cancel&&he(o.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:o.cancelButtonStyle||we,onClick:l=>{var p,y;he(o.cancel)&&q&&((y=(p=o.cancel).onClick)==null||y.call(p,l),D())},className:A(f?.cancelButton,(w=o?.classNames)==null?void 0:w.cancelButton)},o.cancel.label):null,E.isValidElement(o.action)?o.action:o.action&&he(o.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:o.actionButtonStyle||le,onClick:l=>{var p,y;he(o.action)&&((y=(p=o.action).onClick)==null||y.call(p,l),!l.defaultPrevented&&D())},className:A(f?.actionButton,(N=o?.classNames)==null?void 0:N.actionButton)},o.action.label):null))};function qe(){if(typeof window>"u"||typeof document>"u")return"ltr";let t=document.documentElement.getAttribute("dir");return t==="auto"||!t?window.getComputedStyle(document.documentElement).direction:t}function Sa(t,e){let a={};return[t,e].forEach((r,s)=>{let i=s===1,u=i?"--mobile-offset":"--offset",m=i?va:ga;function d(h){["top","right","bottom","left"].forEach(w=>{a[`${u}-${w}`]=typeof h=="number"?`${h}px`:h})}typeof r=="number"||typeof r=="string"?d(r):typeof r=="object"?["top","right","bottom","left"].forEach(h=>{r[h]===void 0?a[`${u}-${h}`]=m:a[`${u}-${h}`]=typeof r[h]=="number"?`${r[h]}px`:r[h]}):d(m)}),a}var Ca=E.forwardRef(function(t,e){let{invert:a,position:r="bottom-right",hotkey:s=["altKey","KeyT"],expand:i,closeButton:u,className:m,offset:d,mobileOffset:h,theme:w="light",richColors:N,duration:Q,style:o,visibleToasts:ye=pa,toastOptions:T,dir:O=qe(),gap:se=ba,loadingIcon:W,icons:B,containerAriaLabel:be="Notifications",pauseWhenPageIsHidden:X}=t,[k,Z]=n.useState([]),ne=n.useMemo(()=>Array.from(new Set([r].concat(k.filter(g=>g.position).map(g=>g.position)))),[k,r]),[ie,we]=n.useState([]),[le,z]=n.useState(!1),[ce,U]=n.useState(!1),[de,$]=n.useState(w!=="system"?w:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),L=n.useRef(null),ue=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),f=n.useRef(null),P=n.useRef(!1),xe=n.useCallback(g=>{Z(v=>{var b;return(b=v.find(S=>S.id===g.id))!=null&&b.delete||C.dismiss(g.id),v.filter(({id:S})=>S!==g.id)})},[]);return n.useEffect(()=>C.subscribe(g=>{if(g.dismiss){Z(v=>v.map(b=>b.id===g.id?{...b,delete:!0}:b));return}setTimeout(()=>{Pt.flushSync(()=>{Z(v=>{let b=v.findIndex(S=>S.id===g.id);return b!==-1?[...v.slice(0,b),{...v[b],...g},...v.slice(b+1)]:[g,...v]})})})}),[]),n.useEffect(()=>{if(w!=="system"){$(w);return}if(w==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?$("dark"):$("light")),typeof window>"u")return;let g=window.matchMedia("(prefers-color-scheme: dark)");try{g.addEventListener("change",({matches:v})=>{$(v?"dark":"light")})}catch{g.addListener(({matches:b})=>{try{$(b?"dark":"light")}catch(S){console.error(S)}})}},[w]),n.useEffect(()=>{k.length<=1&&z(!1)},[k]),n.useEffect(()=>{let g=v=>{var b,S;s.every(K=>v[K]||v.code===K)&&(z(!0),(b=L.current)==null||b.focus()),v.code==="Escape"&&(document.activeElement===L.current||(S=L.current)!=null&&S.contains(document.activeElement))&&z(!1)};return document.addEventListener("keydown",g),()=>document.removeEventListener("keydown",g)},[s]),n.useEffect(()=>{if(L.current)return()=>{f.current&&(f.current.focus({preventScroll:!0}),f.current=null,P.current=!1)}},[L.current]),n.createElement("section",{ref:e,"aria-label":`${be} ${ue}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},ne.map((g,v)=>{var b;let[S,K]=g.split("-");return k.length?n.createElement("ol",{key:g,dir:O==="auto"?qe():O,tabIndex:-1,ref:L,className:m,"data-sonner-toaster":!0,"data-theme":de,"data-y-position":S,"data-lifted":le&&k.length>1&&!i,"data-x-position":K,style:{"--front-toast-height":`${((b=ie[0])==null?void 0:b.height)||0}px`,"--width":`${ya}px`,"--gap":`${se}px`,...o,...Sa(d,h)},onBlur:x=>{P.current&&!x.currentTarget.contains(x.relatedTarget)&&(P.current=!1,f.current&&(f.current.focus({preventScroll:!0}),f.current=null))},onFocus:x=>{x.target instanceof HTMLElement&&x.target.dataset.dismissible==="false"||P.current||(P.current=!0,f.current=x.relatedTarget)},onMouseEnter:()=>z(!0),onMouseMove:()=>z(!0),onMouseLeave:()=>{ce||z(!1)},onDragEnd:()=>z(!1),onPointerDown:x=>{x.target instanceof HTMLElement&&x.target.dataset.dismissible==="false"||U(!0)},onPointerUp:()=>U(!1)},k.filter(x=>!x.position&&v===0||x.position===g).map((x,Ee)=>{var ee,me;return n.createElement(Ta,{key:x.id,icons:B,index:Ee,toast:x,defaultRichColors:N,duration:(ee=T?.duration)!=null?ee:Q,className:T?.className,descriptionClassName:T?.descriptionClassName,invert:a,visibleToasts:ye,closeButton:(me=T?.closeButton)!=null?me:u,interacting:ce,position:g,style:T?.style,unstyled:T?.unstyled,classNames:T?.classNames,cancelButtonStyle:T?.cancelButtonStyle,actionButtonStyle:T?.actionButtonStyle,removeToast:xe,toasts:k.filter(te=>te.position==x.position),heights:ie.filter(te=>te.position==x.position),setHeights:we,expandByDefault:i,gap:se,loadingIcon:W,expanded:le,pauseWhenPageIsHidden:X,swipeDirections:t.swipeDirections})})):null}))});const Na=({...t})=>c.jsx(Ca,{theme:"dark",className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t}),Pa=Ct,ja=E.forwardRef(({className:t,sideOffset:e=4,...a},r)=>c.jsx(at,{ref:r,sideOffset:e,className:H("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...a}));ja.displayName=at.displayName;const ka=E.createContext(void 0);function Ia({children:t}){const e="dark";E.useEffect(()=>{const s=document.documentElement;s.classList.remove("light"),s.classList.add("dark"),localStorage.removeItem("suz-theme");const i=document.querySelector('meta[name="theme-color"]');i&&i.setAttribute("content","#111827")},[]);const a=()=>{},r=()=>{};return c.jsx(ka.Provider,{value:{theme:e,toggleTheme:a,setTheme:r},children:t})}const Ma={LCP:{good:2500,poor:4e3},FID:{good:100,poor:300},CLS:{good:.1,poor:.25},FCP:{good:1800,poor:3e3},TTFB:{good:800,poor:1800}};function re(t,e){const a=Ma[t];return e<=a.good?"good":e<=a.poor?"needs-improvement":"poor"}class ct{constructor(e){this.onMetric=e,this.initializeObservers()}metrics={};observers=[];initializeObservers(){this.observeLCP(),this.observeFID(),this.observeCLS(),this.observeFCP(),this.observeTTFB()}observeLCP(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{const r=a.getEntries(),s=r[r.length-1];if(s){const i=s.renderTime||s.loadTime||s.startTime,u={name:"LCP",value:i,rating:re("LCP",i),delta:i-(this.metrics.lcp?.value||0),id:`lcp-${Date.now()}`};this.metrics.lcp=u,this.onMetric?.(u)}});e.observe({type:"largest-contentful-paint",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] LCP observer failed:",e)}}observeFID(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{a.getEntries().forEach(s=>{const i=s.processingStart-s.startTime,u={name:"FID",value:i,rating:re("FID",i),delta:i-(this.metrics.fid?.value||0),id:`fid-${Date.now()}`};this.metrics.fid=u,this.onMetric?.(u)})});e.observe({type:"first-input",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] FID observer failed:",e)}}observeCLS(){if("PerformanceObserver"in window)try{let e=0,a=0,r=[];const s=new PerformanceObserver(i=>{i.getEntries().forEach(m=>{if(!m.hadRecentInput){const d=r[0],h=r[r.length-1];if(a&&m.startTime-h.startTime<1e3&&m.startTime-d.startTime<5e3?(a+=m.value,r.push(m)):(a=m.value,r=[m]),a>e){e=a;const w={name:"CLS",value:e,rating:re("CLS",e),delta:e-(this.metrics.cls?.value||0),id:`cls-${Date.now()}`};this.metrics.cls=w,this.onMetric?.(w)}}})});s.observe({type:"layout-shift",buffered:!0}),this.observers.push(s)}catch(e){console.warn("[Performance] CLS observer failed:",e)}}observeFCP(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver(a=>{a.getEntries().forEach(s=>{if(s.name==="first-contentful-paint"){const i=s.startTime,u={name:"FCP",value:i,rating:re("FCP",i),delta:i-(this.metrics.fcp?.value||0),id:`fcp-${Date.now()}`};this.metrics.fcp=u,this.onMetric?.(u)}})});e.observe({type:"paint",buffered:!0}),this.observers.push(e)}catch(e){console.warn("[Performance] FCP observer failed:",e)}}observeTTFB(){try{const e=performance.getEntriesByType("navigation")[0];if(e){const a=e.responseStart-e.requestStart,r={name:"TTFB",value:a,rating:re("TTFB",a),delta:a-(this.metrics.ttfb?.value||0),id:`ttfb-${Date.now()}`};this.metrics.ttfb=r,this.onMetric?.(r)}}catch(e){console.warn("[Performance] TTFB measurement failed:",e)}}getMetrics(){return{...this.metrics}}getPerformanceScore(){const e=Object.values(this.metrics);if(e.length===0)return 0;const a=e.map(r=>{switch(r.rating){case"good":return 100;case"needs-improvement":return 75;case"poor":return 50;default:return 0}});return Math.round(a.reduce((r,s)=>r+s,0)/a.length)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}class pe{static preloadCriticalResources(){[{href:"/assets/logos/logo.png",as:"image"}].forEach(({href:a,as:r})=>{const s=document.createElement("link");s.rel="preload",s.href=a,s.as=r,document.head.appendChild(s)})}static optimizeImages(){document.querySelectorAll("img[data-optimize]").forEach(a=>{const r=a;r.loading||(r.loading="lazy"),r.decoding="async",(!r.width||!r.height)&&(r.style.aspectRatio="16/9")})}static reduceLayoutShifts(){document.querySelectorAll("img:not([width]):not([height])").forEach(r=>{const s=r;s.style.aspectRatio="16/9",s.style.width="100%",s.style.height="auto"}),document.querySelectorAll("[data-dynamic]").forEach(r=>{const s=r;s.style.minHeight||(s.style.minHeight="200px")})}static optimizeFonts(){const e=document.createElement("style");e.textContent=`
      @font-face {
        font-family: -apple-system;
        font-display: swap;
      }
    `,document.head.appendChild(e)}}let Je=null;function Ra(){Je||(Je=new ct(t=>{console.log(`[Performance] ${t.name}:`,{value:Math.round(t.value),rating:t.rating,delta:Math.round(t.delta)})}),pe.preloadCriticalResources(),pe.optimizeImages(),pe.reduceLayoutShifts(),pe.optimizeFonts(),console.log("[Performance] Performance monitoring initialized"))}const Aa=E.lazy(()=>Ie(()=>import("./Index-DIxC5n1W.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))),Oa=E.lazy(()=>Ie(()=>import("./NotFound-Cpc_r-E2.js"),__vite__mapDeps([10,1,2,6]))),za=E.lazy(()=>Ie(()=>import("./CookieConsent-BGXdIGSL.js"),__vite__mapDeps([11,1,2,7,8,4]))),La=()=>c.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:c.jsxs("div",{className:"text-center space-y-4",children:[c.jsx("div",{className:"w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"}),c.jsx("p",{className:"text-slate-600 font-medium",children:"Lädt..."})]})}),Ba=new Rt,Da=({children:t})=>{const e=Lt();return E.useEffect(()=>{Bt(),Ve.monitorCoreWebVitals(),Ve.monitorAnimationPerformance();const a=new ct(r=>{typeof window<"u"&&window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:r.name,value:Math.round(r.value),custom_parameter_1:r.rating})});return Ye({}),_t(Ft()),()=>{a.disconnect()}},[]),E.useEffect(()=>{Dt(e.pathname,document.title),e.pathname!=="/"&&Ye({title:`${document.title} | SUZ Reinigung`,url:`https://www.suzreinigung.de${e.pathname}`})},[e]),c.jsx(c.Fragment,{children:t})},_a=()=>c.jsx(At,{client:Ba,children:c.jsx(Ia,{children:c.jsxs(Pa,{children:[c.jsx(Xt,{}),c.jsx(Na,{}),c.jsx(Ot,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:c.jsxs(Da,{children:[c.jsx(E.Suspense,{fallback:c.jsx(La,{}),children:c.jsxs(zt,{children:[c.jsx($e,{path:"/",element:c.jsx(Aa,{})}),c.jsx($e,{path:"*",element:c.jsx(Oa,{})})]})}),c.jsx(E.Suspense,{fallback:null,children:c.jsx(za,{})})]})})]})})}),Me={IMAGES:{name:"suz-images",version:"1.0.0",maxAge:24*60*60*1e3,maxEntries:100},API:{name:"suz-api",version:"1.0.0",maxAge:5*60*1e3,maxEntries:50},STATIC:{name:"suz-static",version:"1.0.0",maxAge:7*24*60*60*1e3,maxEntries:200}};class Re{config;storageKey;constructor(e){this.config=e,this.storageKey=`${e.name}-${e.version}`}async set(e,a){try{const r=Date.now(),s={data:a,timestamp:r,expires:r+this.config.maxAge},i=await this.getCache();i[e]=s,await this.enforceMaxEntries(i),localStorage.setItem(this.storageKey,JSON.stringify(i))}catch(r){console.warn("[Cache] Failed to set cache entry:",r)}}async get(e){try{const a=await this.getCache(),r=a[e];return r?Date.now()>r.expires?(delete a[e],localStorage.setItem(this.storageKey,JSON.stringify(a)),null):r.data:null}catch(a){return console.warn("[Cache] Failed to get cache entry:",a),null}}async has(e){return await this.get(e)!==null}async delete(e){try{const a=await this.getCache();delete a[e],localStorage.setItem(this.storageKey,JSON.stringify(a))}catch(a){console.warn("[Cache] Failed to delete cache entry:",a)}}async clear(){try{localStorage.removeItem(this.storageKey)}catch(e){console.warn("[Cache] Failed to clear cache:",e)}}async getStats(){try{const e=await this.getCache(),a=Object.values(e),r=a.map(s=>s.timestamp);return{size:JSON.stringify(e).length,entries:a.length,oldestEntry:r.length>0?Math.min(...r):null,newestEntry:r.length>0?Math.max(...r):null}}catch(e){return console.warn("[Cache] Failed to get cache stats:",e),{size:0,entries:0,oldestEntry:null,newestEntry:null}}}async cleanup(){try{const e=await this.getCache(),a=Date.now();let r=0;for(const[s,i]of Object.entries(e))a>i.expires&&(delete e[s],r++);return r>0&&localStorage.setItem(this.storageKey,JSON.stringify(e)),r}catch(e){return console.warn("[Cache] Failed to cleanup cache:",e),0}}async getCache(){try{const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):{}}catch(e){return console.warn("[Cache] Failed to parse cache, resetting:",e),{}}}async enforceMaxEntries(e){const a=Object.entries(e);if(a.length<=this.config.maxEntries)return;a.sort(([,s],[,i])=>s.timestamp-i.timestamp);const r=a.length-this.config.maxEntries;for(let s=0;s<r;s++)delete e[a[s][0]]}}const dt=new Re(Me.IMAGES),Fa=new Re(Me.API),$a=new Re(Me.STATIC);async function Va(){const e=["/assets/logos/logo.png"].map(async a=>{try{const r=await fetch(a);if(r.ok){const s=await r.blob();await dt.set(a,URL.createObjectURL(s))}}catch(r){console.warn(`[Cache] Failed to preload ${a}:`,r)}});await Promise.allSettled(e)}async function Ya(){await Promise.all([dt.cleanup(),Fa.cleanup(),$a.cleanup()]),await Va(),console.log("[Cache] Cache system initialized")}"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(t=>{console.log("SW registered: ",t),t.addEventListener("updatefound",()=>{const e=t.installing;e&&e.addEventListener("statechange",()=>{e.state==="installed"&&navigator.serviceWorker.controller&&confirm("Neue Version verfügbar. Seite neu laden?")&&(e.postMessage({type:"SKIP_WAITING"}),window.location.reload())})})}).catch(t=>{console.log("SW registration failed: ",t)})});Ya().catch(console.error);Ra();document.body.classList.add("force-apple-design");rt(document.getElementById("root")).render(c.jsx(_a,{}));export{Ie as _};
