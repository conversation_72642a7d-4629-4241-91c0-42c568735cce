interface SpeedInsightsProps {
    dsn?: string;
    sampleRate?: number;
    route?: string | null;
    beforeSend?: BeforeSendMiddleware;
    debug?: boolean;
    scriptSrc?: string;
    endpoint?: string;
}
type EventTypes = 'vital';
interface Event {
    type: EventTypes;
    url: string;
}
type BeforeSendMiddleware = (data: Event) => Event | null | undefined | false;
interface Functions {
    beforeSend?: BeforeSendMiddleware;
}
interface SpeedInsights<T extends keyof Functions = keyof Functions> {
    queue: [T, Functions[T]][];
    addAction: (action: T, data: Functions[T]) => void;
}
declare global {
    interface Window {
        /** Base interface to track events */
        si?: SpeedInsights['addAction'];
        /** Queue for speed insights datapoints, before the library is loaded */
        siq?: SpeedInsights['queue'];
        sil?: boolean;
        /** used by Astro component only */
        speedInsightsBeforeSend?: BeforeSendMiddleware;
    }
}

declare function computeRoute(pathname: string | null, pathParams: Record<string, string | string[]> | null): string | null;

/**
 * Injects the Vercel Speed Insights script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/speed-insights).
 * @param [props] - Speed Insights options.
 * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.
 * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.
 * @param [props.sampleRate] - When setting to 0.5, 50% of the events will be sent to Vercel Speed Insights. Defaults to `1`.
 * @param [props.route] - The dynamic route of the page.
 * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.
 */
declare function injectSpeedInsights(props?: SpeedInsightsProps & {
    framework?: string;
    basePath?: string;
}): {
    setRoute: (route: string | null) => void;
} | null;

declare const _default: {
    injectSpeedInsights: typeof injectSpeedInsights;
    computeRoute: typeof computeRoute;
};

export { type SpeedInsightsProps, computeRoute, _default as default, injectSpeedInsights };
