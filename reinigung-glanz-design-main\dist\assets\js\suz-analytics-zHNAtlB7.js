import{i as s,a as l}from"./analytics-vendor-vOw2-8uu.js";const d=void 0;const a={ga4:{measurementId:d,enabled:!1},vercel:{enabled:!0,debug:!1},speedInsights:{enabled:!0,debug:!1}},m=()=>{try{a.vercel.enabled&&(s({debug:a.vercel.debug}),console.log("✅ Vercel Analytics initialized")),a.speedInsights.enabled&&(l({debug:a.speedInsights.debug}),console.log("✅ Vercel Speed Insights initialized")),a.ga4.enabled}catch(e){console.error("❌ Analytics initialization failed:",e)}};const r=(e,t)=>{try{a.vercel.enabled&&analytics.track(e,t),a.ga4.enabled&&window.gtag}catch(i){console.error("❌ Event tracking failed:",i)}},f=(e,t)=>{try{a.ga4.enabled&&window.gtag}catch(i){console.error("❌ Page view tracking failed:",i)}},c={serviceInquiry:e=>{r("service_inquiry",{service_type:e,event_category:"lead_generation",event_label:`Service: ${e}`})},contactFormSubmit:e=>{r("contact_form_submit",{form_type:e,event_category:"lead_generation",event_label:`Form: ${e}`})},phoneClick:()=>{r("phone_click",{event_category:"contact",event_label:"Phone number clicked"})},emailClick:()=>{r("email_click",{event_category:"contact",event_label:"Email address clicked"})},servicePageView:e=>{r("service_page_view",{service_type:e,event_category:"engagement",event_label:`Service page: ${e}`})},testimonialView:e=>{r("testimonial_view",{company_name:e,event_category:"engagement",event_label:`Testimonial: ${e}`})},performanceMetric:(e,t)=>{r("performance_metric",{metric_name:e,metric_value:t,event_category:"performance",event_label:`${e}: ${t}`})}},_={grant:()=>{localStorage.setItem("analytics_consent","granted")},deny:()=>{localStorage.setItem("analytics_consent","denied")},hasConsent:()=>localStorage.getItem("analytics_consent")==="granted"},v={monitorCoreWebVitals:()=>{"web-vital"in window||"performance"in window&&window.addEventListener("load",()=>{const e=performance.getEntriesByType("navigation")[0];if(e){const t={loadTime:e.loadEventEnd-e.loadEventStart,domContentLoaded:e.domContentLoadedEventEnd-e.domContentLoadedEventStart,firstPaint:0,firstContentfulPaint:0};performance.getEntriesByType("paint").forEach(n=>{n.name==="first-paint"?t.firstPaint=n.startTime:n.name==="first-contentful-paint"&&(t.firstContentfulPaint=n.startTime)}),Object.entries(t).forEach(([n,o])=>{o>0&&c.performanceMetric(n,Math.round(o))})}})},monitorAnimationPerformance:()=>{let e=0,t=performance.now();const i=()=>{e++;const n=performance.now();if(n-t>=1e3){const o=Math.round(e*1e3/(n-t));o<55&&c.performanceMetric("low_fps_detected",o),e=0,t=n}requestAnimationFrame(i)};requestAnimationFrame(i)}};export{_ as a,m as i,v as p,f as t};
