var o="@vercel/analytics",u="1.5.0",f=()=>{window.va||(window.va=function(...n){(window.vaq=window.vaq||[]).push(n)})};function d(){return typeof window<"u"}function s(){try{const e="production"}catch{}return"production"}function l(e="auto"){if(e==="auto"){window.vam=s();return}window.vam=e}function v(){return(d()?window.vam:s())||"production"}function r(){return v()==="development"}function w(e){return e.scriptSrc?e.scriptSrc:r()?"https://va.vercel-scripts.com/v1/script.debug.js":e.basePath?`${e.basePath}/insights/script.js`:"/_vercel/insights/script.js"}function y(e={debug:!0}){var n;if(!d())return;l(e.mode),f(),e.beforeSend&&((n=window.va)==null||n.call(window,"beforeSend",e.beforeSend));const i=w(e);if(document.head.querySelector(`script[src*="${i}"]`))return;const t=document.createElement("script");t.src=i,t.defer=!0,t.dataset.sdkn=o+(e.framework?`/${e.framework}`:""),t.dataset.sdkv=u,e.disableAutoTrack&&(t.dataset.disableAutoTrack="1"),e.endpoint?t.dataset.endpoint=e.endpoint:e.basePath&&(t.dataset.endpoint=`${e.basePath}/insights`),e.dsn&&(t.dataset.dsn=e.dsn),t.onerror=()=>{const a=r()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log(`[Vercel Web Analytics] Failed to load script from ${i}. ${a}`)},r()&&e.debug===!1&&(t.dataset.debug="false"),document.head.appendChild(t)}var m="@vercel/speed-insights",h="1.2.0",b=()=>{window.si||(window.si=function(...n){(window.siq=window.siq||[]).push(n)})};function g(){return typeof window<"u"}function S(){try{const e="production"}catch{}return"production"}function c(){return S()==="development"}function $(e){return e.scriptSrc?e.scriptSrc:c()?"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js":e.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":e.basePath?`${e.basePath}/speed-insights/script.js`:"/_vercel/speed-insights/script.js"}function k(e={}){var n;if(!g()||e.route===null)return null;b();const i=$(e);if(document.head.querySelector(`script[src*="${i}"]`))return null;e.beforeSend&&((n=window.si)==null||n.call(window,"beforeSend",e.beforeSend));const t=document.createElement("script");return t.src=i,t.defer=!0,t.dataset.sdkn=m+(e.framework?`/${e.framework}`:""),t.dataset.sdkv=h,e.sampleRate&&(t.dataset.sampleRate=e.sampleRate.toString()),e.route&&(t.dataset.route=e.route),e.endpoint?t.dataset.endpoint=e.endpoint:e.basePath&&(t.dataset.endpoint=`${e.basePath}/speed-insights/vitals`),e.dsn&&(t.dataset.dsn=e.dsn),c()&&e.debug===!1&&(t.dataset.debug="false"),t.onerror=()=>{console.log(`[Vercel Speed Insights] Failed to load script from ${i}. Please check if any content blockers are enabled and try again.`)},document.head.appendChild(t),{setRoute:a=>{t.dataset.route=a??void 0}}}export{k as a,y as i};
