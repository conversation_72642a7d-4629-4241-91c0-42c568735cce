import{j as e}from"./ui-vendor-WY5xTeZk.js";import{r as t}from"./react-vendor-o6ozJo2K.js";import{X as r}from"./icons-vendor-B9WvW-j2.js";const u=()=>{const[i,n]=t.useState(!1),[l,s]=t.useState(!1),a=()=>e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsx("div",{className:"suz-card-glass rounded-3xl border border-white/30 shadow-2xl max-w-4xl max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6 md:p-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"suz-text-heading-xl gradient-text-animated",children:"Impressum"}),e.jsx("button",{type:"button",onClick:()=>n(!1),className:"p-2 hover:bg-white/10 rounded-full transition-colors suz-focus-ring","aria-label":"Impressum schließen",children:e.jsx(r,{className:"w-6 h-6 text-slate-300"})})]}),e.jsxs("div",{className:"space-y-6 text-slate-300 suz-text-body-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"S.U.Z. Schutz und Sicherheit GmbH"}),e.jsxs("p",{children:[e.jsx("strong",{children:"Adresse:"}),e.jsx("br",{}),"Paul-Langen-Straße 39",e.jsx("br",{}),"53229 Bonn"]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Kontakt:"}),e.jsxs("p",{children:["Telefon: +49 228 50461294",e.jsx("br",{}),"Fax: +49 228 50461294",e.jsx("br",{}),"E-Mail: <EMAIL>"]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Vertreten durch:"}),e.jsx("p",{children:"Geschäftsführer: Ali Mohamed"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Registereintrag:"}),e.jsxs("p",{children:["Eintragung im Handelsregister.",e.jsx("br",{}),"Registergericht: Amtsgericht, Köln",e.jsx("br",{}),"Registernummer: HRB 119388"]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Umsatzsteuer-ID:"}),e.jsx("p",{children:"Umsatzsteuer-Identifikationsnummer gemäß §27 a Umsatzsteuergesetz: 206/5948/1829 NAST 1"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Haftungsausschluss:"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-slate-200 font-medium",children:"Haftung für Inhalte:"}),e.jsx("p",{children:"Die Inhalte unserer Seiten wurden mit größter Sorgfalt erstellt. Für die Richtigkeit, Vollständigkeit und Aktualität der Inhalte können wir jedoch keine Gewähr übernehmen."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-slate-200 font-medium",children:"Haftung für Links:"}),e.jsx("p",{children:"Unser Angebot enthält Links zu externen Webseiten Dritter, auf deren Inhalte wir keinen Einfluss haben. Deshalb können wir für diese fremden Inhalte auch keine Gewähr übernehmen."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-slate-200 font-medium",children:"Urheberrecht:"}),e.jsx("p",{children:"Die durch die Seitenbetreiber erstellten Inhalte und Werke auf diesen Seiten unterliegen dem deutschen Urheberrecht. Beiträge Dritter sind als solche gekennzeichnet."})]})]})]})]})]})})}),d=()=>e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsx("div",{className:"suz-card-glass rounded-3xl border border-white/30 shadow-2xl max-w-4xl max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6 md:p-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"suz-text-heading-xl gradient-text-animated",children:"Datenschutzerklärung"}),e.jsx("button",{type:"button",onClick:()=>s(!1),className:"p-2 hover:bg-white/10 rounded-full transition-colors suz-focus-ring","aria-label":"Datenschutzerklärung schließen",children:e.jsx(r,{className:"w-6 h-6 text-slate-300"})})]}),e.jsxs("div",{className:"space-y-6 text-slate-300 suz-text-body-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Verantwortlicher:"}),e.jsxs("p",{children:["S.U.Z. Schutz und Sicherheit GmbH",e.jsx("br",{}),"Paul-Langen-Straße 39",e.jsx("br",{}),"53229 Bonn",e.jsx("br",{}),"Telefon: +*************",e.jsx("br",{}),"E-Mail: <EMAIL>"]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Allgemeine Hinweise:"}),e.jsx("p",{children:"Der Schutz Ihrer persönlichen Daten ist uns ein besonderes Anliegen. Ihre Daten werden im Rahmen der gesetzlichen Vorschriften geschützt."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Erhebung und Verarbeitung personenbezogener Daten:"}),e.jsx("p",{children:"Wir erheben und verarbeiten personenbezogene Daten nur, soweit dies zur Bereitstellung einer funktionsfähigen Website sowie unserer Inhalte und Leistungen erforderlich ist. Die Erhebung und Verarbeitung personenbezogener Daten erfolgt regelmäßig nur nach Einwilligung des Nutzers. Eine Ausnahme gilt in solchen Fällen, in denen eine vorherige Einholung einer Einwilligung aus tatsächlichen Gründen nicht möglich ist und die Verarbeitung der Daten durch gesetzliche Vorschriften gestattet ist."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Nutzung und Weitergabe personenbezogener Daten:"}),e.jsx("p",{children:"Personenbezogene Daten werden nur dann an Dritte weitergegeben, wenn dies im Rahmen der Vertragsabwicklung notwendig ist oder Sie eingewilligt haben. Eine Weitergabe der Daten an Dritte ohne Einwilligung des Betroffenen erfolgt nicht."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Rechte der betroffenen Personen:"}),e.jsx("p",{children:"Sie haben das Recht auf Auskunft, Berichtigung, Löschung, Einschränkung der Verarbeitung, Datenübertragbarkeit sowie Widerspruch gegen die Verarbeitung Ihrer personenbezogenen Daten. Hierzu können Sie sich jederzeit an uns wenden."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Recht auf Beschwerde bei einer Aufsichtsbehörde:"}),e.jsx("p",{children:"Im Falle datenschutzrechtlicher Verstöße steht dem Betroffenen ein Beschwerderecht bei der zuständigen Aufsichtsbehörde zu."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Datensicherheit:"}),e.jsx("p",{children:"Wir setzen technische und organisatorische Sicherheitsmaßnahmen ein, um Ihre Daten gegen zufällige oder vorsätzliche Manipulationen, Verlust, Zerstörung oder gegen den Zugriff unberechtigter Personen zu schützen. Unsere Sicherheitsmaßnahmen werden entsprechend der technologischen Entwicklung fortlaufend verbessert."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Cookies:"}),e.jsx("p",{children:"Unsere Internetseiten verwenden Cookies. Cookies sind kleine Textdateien, die auf Ihrem Rechner abgelegt werden und die Ihr Browser speichert. Sie dienen dazu, unser Angebot nutzerfreundlicher, effektiver und sicherer zu machen. Sie können die Installation der Cookies durch eine entsprechende Einstellung Ihrer Browser-Software verhindern; wir weisen jedoch darauf hin, dass in diesem Fall gegebenenfalls nicht sämtliche Funktionen dieser Website vollumfänglich genutzt werden können."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Server-Log-Files:"}),e.jsx("p",{children:"Der Provider der Seiten erhebt und speichert automatisch Informationen in so genannten Server-Log Files, die Ihr Browser automatisch an uns übermittelt. Dies sind: Browsertyp und Browserversion, verwendetes Betriebssystem, Referrer URL, Hostname des zugreifenden Rechners, Uhrzeit der Serveranfrage und IP-Adresse. Eine Zusammenführung dieser Daten mit anderen Datenquellen wird nicht vorgenommen."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Kontaktformular:"}),e.jsx("p",{children:"Wenn Sie uns per Kontaktformular Anfragen zukommen lassen, werden Ihre Angaben aus dem Anfrageformular inklusive der von Ihnen dort angegebenen Kontaktdaten zwecks Bearbeitung der Anfrage und für den Fall von Anschlussfragen bei uns gespeichert. Diese Daten geben wir nicht ohne Ihre Einwilligung weiter."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Google Analytics:"}),e.jsx("p",{children:'Diese Website nutzt Funktionen des Webanalysedienstes Google Analytics. Anbieter ist die Google Inc., 1600 Amphitheatre Parkway Mountain View, CA 94043, USA. Google Analytics verwendet so genannte "Cookies". Die durch den Cookie erzeugten Informationen über Ihre Benutzung dieser Website werden in der Regel an einen Server von Google in den USA übertragen und dort gespeichert.'})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Änderungen der Datenschutzerklärung:"}),e.jsx("p",{children:"Wir behalten uns vor, diese Datenschutzerklärung anzupassen, damit sie stets den aktuellen rechtlichen Anforderungen entspricht oder um Änderungen unserer Leistungen in der Datenschutzerklärung umzusetzen, z. B. bei der Einführung neuer Services. Für Ihren erneuten Besuch gilt dann die neue Datenschutzerklärung."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-slate-200 font-semibold mb-2",children:"Kontakt:"}),e.jsxs("p",{children:["Für weitere Fragen zum Thema Datenschutz können Sie sich jederzeit an uns wenden:",e.jsx("br",{}),"S.U.Z. Schutz und Sicherheit GmbH",e.jsx("br",{}),"Paul-Langen-Straße 39",e.jsx("br",{}),"53229 Bonn",e.jsx("br",{}),"E-Mail: <EMAIL>"]})]})]})]})})});return e.jsxs(e.Fragment,{children:[e.jsx("footer",{className:"py-8 px-4",role:"contentinfo","aria-label":"Website Footer",children:e.jsx("div",{className:"max-w-6xl mx-auto",children:e.jsx("div",{className:"suz-card-glass rounded-2xl border border-white/20 px-6 py-6",children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsx("p",{className:"suz-text-body-lg text-slate-300 text-center",children:"© 2025 SUZ Schutz und Sicherheit GmbH"}),e.jsxs("div",{className:"flex items-center gap-6 text-sm text-slate-400",children:[e.jsx("button",{type:"button",onClick:()=>s(!0),className:"hover:text-slate-200 transition-colors duration-200 suz-focus-ring","aria-label":"Datenschutz-Informationen",children:"Datenschutz"}),e.jsx("button",{type:"button",onClick:()=>n(!0),className:"hover:text-slate-200 transition-colors duration-200 suz-focus-ring","aria-label":"Impressum anzeigen",children:"Impressum"})]}),e.jsxs("p",{className:"text-xs text-slate-500 text-center",children:["Diese Seite wurde von"," ",e.jsx("a",{href:"https://www.econicmedia.pro/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors duration-200 suz-focus-ring","aria-label":"EconicMedia Website besuchen",children:"EconicMedia"})," ","erstellt."]})]})})})}),i&&e.jsx(a,{}),l&&e.jsx(d,{})]})};export{u as default};
