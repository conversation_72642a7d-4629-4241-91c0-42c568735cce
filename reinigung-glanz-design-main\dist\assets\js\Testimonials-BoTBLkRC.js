import{j as e}from"./ui-vendor-WY5xTeZk.js";import{r as o}from"./react-vendor-o6ozJo2K.js";import{Q as c,i as d,G as u,U as m,b as g,H as p,c as h,B as x,d as f}from"./icons-vendor-B9WvW-j2.js";const b=[{id:"1",companyName:"Hotel Excelsior Ernst Köln",companyType:"hotel",contactPerson:"<PERSON>",position:"Hotelmanager",testimonialText:"SUZ Reinigung überzeugt uns täglich mit höchster Professionalität und Zuverlässigkeit. Unsere Gäste schätzen die makellose Sauberkeit in allen Bereichen.",rating:5},{id:"2",companyName:"Büropark Rheinauhafen",companyType:"office",contactPerson:"Frau Weber",position:"Facility Managerin",testimonialText:"Seit Jahren vertrauen wir auf SUZ Reinigung. Die Qualität ist konstant hervorragend und das Team arbeitet äußerst diskret und effizient.",rating:5},{id:"3",companyName:"Universitätsklinikum Bonn",companyType:"medical",contactPerson:"Dr. Müller",position:"Hygienebeauftragte",testimonialText:"In unserem sensiblen Umfeld sind höchste Hygienestandards essentiell. SUZ Reinigung erfüllt alle Anforderungen mit absoluter Präzision.",rating:5},{id:"4",companyName:"Restaurant Hanse-Klause",companyType:"restaurant",contactPerson:"Herr Koch",position:"Restaurantleiter",testimonialText:"Pünktlichkeit und Gründlichkeit - SUZ Reinigung sorgt dafür, dass unser Restaurant jeden Tag in perfektem Zustand für unsere Gäste bereit ist.",rating:5},{id:"5",companyName:"Galeria Kaufhof Köln",companyType:"retail",contactPerson:"Frau Becker",position:"Store Managerin",testimonialText:"Ein sauberes Einkaufserlebnis ist für uns entscheidend. SUZ Reinigung gewährleistet dies mit ihrer professionellen und zuverlässigen Arbeit.",rating:5},{id:"6",companyName:"Universität zu Köln",companyType:"school",contactPerson:"Prof. Dr. Wagner",position:"Verwaltungsdirektor",testimonialText:"Für unsere Studierenden und Mitarbeiter ist eine saubere Lernumgebung wichtig. SUZ Reinigung liefert konstant exzellente Ergebnisse.",rating:5}],v=a=>({hotel:f,office:x,medical:h,residential:p,retail:g,restaurant:m,school:u})[a],z=()=>{const a=o.useRef(null);return o.useEffect(()=>{const t=a.current;if(!t)return;if(window.matchMedia("(prefers-reduced-motion: reduce)").matches){t.querySelectorAll(".suz-testimonial-card").forEach(r=>{r.classList.add("animate-fade-in")});return}const s=new IntersectionObserver(n=>{n.forEach(r=>{r.isIntersecting&&r.target.classList.add("animate-fade-in")})},{threshold:.1,rootMargin:"50px"});return t.querySelectorAll(".suz-testimonial-card").forEach(n=>s.observe(n)),()=>s.disconnect()},[]),e.jsxs("section",{ref:a,className:"suz-testimonials-section bg-premium-gradient relative overflow-hidden suz-section-standard","aria-label":"Kundenbewertungen und Testimonials",role:"region",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-black/5 to-transparent pointer-events-none z-0"}),e.jsxs("div",{className:"max-w-7xl mx-auto relative z-10",children:[e.jsxs("div",{className:"text-center mb-16 px-4 animate-fade-in",children:[e.jsxs("h2",{className:"suz-section-title text-slate-100 mb-8",children:["Was unsere ",e.jsx("span",{className:"gradient-text",children:"Kunden sagen"})]}),e.jsx("p",{className:"suz-text-heading-lg text-slate-300 max-w-3xl mx-auto font-light",children:"Vertrauen Sie auf die Erfahrungen zufriedener Kunden aus der Region Köln-Bonn"})]}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:b.map((t,i)=>{const s=v(t.companyType);return e.jsxs("article",{className:"suz-testimonial-card suz-card-glass glass-morphism-premium rounded-3xl border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-500 group relative overflow-hidden",style:{animationDelay:`${i*.15}s`,padding:"var(--component-padding-lg)"},role:"article","aria-label":`Testimonial von ${t.contactPerson}, ${t.companyName}`,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500 pointer-events-none"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"mb-4 flex justify-between items-start",children:[e.jsx(c,{size:24,className:"text-blue-400 opacity-60","aria-hidden":"true"}),e.jsx("div",{className:"flex gap-1","aria-label":`Bewertung: ${t.rating} von 5 Sternen`,children:[...Array(t.rating)].map((l,n)=>e.jsx(d,{size:16,className:"text-yellow-400 fill-current","aria-hidden":"true"},n))})]}),e.jsxs("blockquote",{className:"suz-text-body-lg text-slate-300 mb-6 suz-german-testimonial-text",children:["„",t.testimonialText,'"']}),e.jsxs("div",{className:"flex items-center gap-4 pt-4 border-t border-white/20",children:[e.jsx("div",{className:"suz-icon-badge-testimonial group-hover:scale-110 group-hover:rotate-3 transition-all duration-500",children:e.jsx(s,{size:24,className:"text-blue-400 drop-shadow-lg transition-all duration-500 group-hover:text-blue-300",strokeWidth:2,"aria-hidden":"true"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"suz-text-body-lg font-semibold text-slate-100 mb-1 group-hover:text-white transition-colors duration-300",children:t.contactPerson}),e.jsx("p",{className:"text-sm text-slate-400 mb-1",children:t.position}),e.jsx("p",{className:"text-sm text-blue-400 font-medium suz-german-business-name",children:t.companyName})]})]})]})]},t.id)})})]})]})};export{z as default};
