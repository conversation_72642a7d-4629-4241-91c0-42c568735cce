const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/Hero-nAro8r8L.js","assets/js/ui-vendor-WY5xTeZk.js","assets/js/react-vendor-o6ozJo2K.js","assets/js/CompanyShowcase-CEuW7WF4.js","assets/js/icons-vendor-B9WvW-j2.js","assets/js/Services-CmZp89uT.js","assets/js/Testimonials-BoTBLkRC.js","assets/js/Contact-qCka4Ijs.js","assets/js/Footer-NkpwNDqH.js"])))=>i.map(i=>d[i]);
import{_ as c}from"./index-0u_6_QRY.js";import{j as e}from"./ui-vendor-WY5xTeZk.js";import{r as n}from"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./icons-vendor-B9WvW-j2.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";import"./suz-analytics-zHNAtlB7.js";import"./analytics-vendor-vOw2-8uu.js";import"./suz-seo-DhXwCDbS.js";const m=768;function b(){const[o,s]=n.useState(void 0);return n.useEffect(()=>{const a=window.matchMedia(`(max-width: ${m-1}px)`),t=()=>{s(window.innerWidth<m)};return a.addEventListener("change",t),s(window.innerWidth<m),()=>a.removeEventListener("change",t)},[]),!!o}const h=({scrollToSection:o})=>{const[s,a]=n.useState(!1),t=b();n.useEffect(()=>{const l=u=>{const d=document.querySelector('[data-nav="main"]');d&&!d.contains(u.target)&&a(!1)};return s?(document.addEventListener("mousedown",l),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("mousedown",l),document.body.style.overflow="unset"}},[s]),n.useEffect(()=>{const l=u=>{u.key==="Escape"&&a(!1)};return s&&document.addEventListener("keydown",l),()=>{document.removeEventListener("keydown",l)}},[s]);const i=l=>{o(l),a(!1)},p=()=>{a(!s)};return e.jsxs(e.Fragment,{children:[!t&&e.jsx("nav",{className:"fixed top-6 z-50 animate-fade-in suz-navigation-enhanced",role:"navigation","aria-label":"Hauptnavigation","data-nav":"main",style:{left:"50%",transform:"translateX(-50%)",width:"auto",maxWidth:"calc(100vw - 2rem)",minWidth:"fit-content",display:"block"},children:e.jsx("div",{className:"suz-card-glass px-4 sm:px-6 md:px-8 py-3 sm:py-4 rounded-full border border-white/30 shadow-xl",children:e.jsxs("div",{className:"flex items-center justify-center space-x-1 sm:space-x-2 md:space-x-4 lg:space-x-6 xl:space-x-8",children:[e.jsx("button",{type:"button",onClick:()=>i("home"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zur Startseite navigieren",children:"Startseite"}),e.jsx("button",{type:"button",onClick:()=>i("services"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zu unseren Leistungen navigieren",children:"Leistungen"}),e.jsx("button",{type:"button",onClick:()=>i("team"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zu unserem Team navigieren",children:"Unser Team"}),e.jsx("button",{type:"button",onClick:()=>i("contact"),className:"suz-nav-link suz-focus-ring whitespace-nowrap","aria-label":"Zum Kontakt navigieren",children:"Kontakt"})]})})}),t&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed top-6 right-6 z-50 animate-fade-in",role:"navigation","aria-label":"Mobile Navigation",children:e.jsx("div",{className:"suz-card-glass px-4 py-3 rounded-full border border-white/30 shadow-xl",children:e.jsx("button",{type:"button",onClick:p,className:"suz-mobile-menu-button suz-focus-ring","aria-label":s?"Menü schließen":"Menü öffnen","aria-expanded":s?"true":"false","aria-controls":"mobile-menu",children:e.jsxs("div",{className:"suz-hamburger-icon",children:[e.jsx("span",{className:`suz-hamburger-line ${s?"suz-hamburger-line-1-open":""}`}),e.jsx("span",{className:`suz-hamburger-line ${s?"suz-hamburger-line-2-open":""}`}),e.jsx("span",{className:`suz-hamburger-line ${s?"suz-hamburger-line-3-open":""}`})]})})})}),s&&e.jsx("div",{className:"suz-mobile-menu-overlay","aria-hidden":"true",children:e.jsx("div",{className:"suz-mobile-menu-backdrop",onClick:()=>a(!1)})}),e.jsx("div",{id:"mobile-menu",className:`suz-mobile-menu ${s?"suz-mobile-menu-open":"suz-mobile-menu-closed"}`,"aria-hidden":s?"false":"true",children:e.jsxs("div",{className:"suz-mobile-menu-content",children:[e.jsx("button",{type:"button",onClick:()=>i("home"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zur Startseite navigieren",children:"Startseite"}),e.jsx("button",{type:"button",onClick:()=>i("services"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zu unseren Leistungen navigieren",children:"Leistungen"}),e.jsx("button",{type:"button",onClick:()=>i("team"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zu unserem Team navigieren",children:"Unser Team"}),e.jsx("button",{type:"button",onClick:()=>i("contact"),className:"suz-mobile-nav-link suz-focus-ring","aria-label":"Zum Kontakt navigieren",children:"Kontakt"})]})})]})]})},x=n.lazy(()=>c(()=>import("./Hero-nAro8r8L.js"),__vite__mapDeps([0,1,2]))),g=n.lazy(()=>c(()=>import("./CompanyShowcase-CEuW7WF4.js"),__vite__mapDeps([3,1,2,4]))),v=n.lazy(()=>c(()=>import("./Services-CmZp89uT.js"),__vite__mapDeps([5,1,2,4]))),f=n.lazy(()=>c(()=>import("./Testimonials-BoTBLkRC.js"),__vite__mapDeps([6,1,2,4]))),j=n.lazy(()=>c(()=>import("./Contact-qCka4Ijs.js"),__vite__mapDeps([7,1,2,4]))),y=n.lazy(()=>c(()=>import("./Footer-NkpwNDqH.js"),__vite__mapDeps([8,1,2,4]))),r=({className:o=""})=>e.jsx("div",{className:`flex items-center justify-center py-16 ${o}`,children:e.jsx("div",{className:"w-8 h-8 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"})}),T=()=>{const[o,s]=n.useState(0);n.useEffect(()=>{const t=()=>s(window.scrollY);return window.addEventListener("scroll",t),()=>window.removeEventListener("scroll",t)},[]),n.useEffect(()=>{console.log("🎨 SUZ Design System Debug:"),console.log("- Premium gradient class applied:",document.querySelector(".bg-premium-gradient")!==null),console.log("- Glass morphism class applied:",document.querySelector(".glass-morphism-premium")!==null),console.log("- Gradient text class applied:",document.querySelector(".gradient-text-animated")!==null),console.log("- Force Apple design class applied:",document.querySelector(".force-apple-design")!==null);const t=getComputedStyle(document.documentElement);console.log("- SUZ Blue Primary:",t.getPropertyValue("--suz-blue-primary")),console.log("- Current theme class:",document.documentElement.classList.contains("dark")?"dark":"light")},[]);const a=t=>{document.getElementById(t)?.scrollIntoView({behavior:"smooth"})};return e.jsxs("div",{className:"min-h-screen bg-premium-gradient overflow-x-hidden force-apple-design",children:[e.jsx(h,{scrollToSection:a}),e.jsx(n.Suspense,{fallback:e.jsx(r,{className:"min-h-screen"}),children:e.jsx(x,{scrollY:o})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(g,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(v,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(f,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(j,{})}),e.jsx(n.Suspense,{fallback:e.jsx(r,{}),children:e.jsx(y,{})})]})};export{T as default};
