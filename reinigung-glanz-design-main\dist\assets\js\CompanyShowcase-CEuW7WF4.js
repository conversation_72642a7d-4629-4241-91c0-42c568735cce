import{j as t}from"./ui-vendor-WY5xTeZk.js";import{r as u}from"./react-vendor-o6ozJo2K.js";import{G as y,U as f,b as x,H as g,c as v,B as w,d as k}from"./icons-vendor-B9WvW-j2.js";const h=[{id:"1",name:"Hotel Excelsior Ernst Köln",type:"hotel"},{id:"2",name:"Büropark Rheinauhafen",type:"office"},{id:"3",name:"Universitätsklinikum Bonn",type:"medical"},{id:"4",name:"Wohnpark Deutzer Freiheit",type:"residential"},{id:"5",name:"Restaurant Hanse-Klause",type:"restaurant"},{id:"6",name:"Galeria Kaufhof Köln",type:"retail"},{id:"7",name:"Hotel Königshof Bonn",type:"hotel"},{id:"8",name:"Bürohaus MediaPark",type:"office"},{id:"9",name:"<PERSON><PERSON><PERSON>",type:"medical"},{id:"10",name:"Residenz Rheinblick",type:"residential"},{id:"11",name:"Brauhaus Sion",type:"restaurant"},{id:"12",name:"Rhein-Center Köln",type:"retail"},{id:"13",name:"Steigenberger Grandhotel",type:"hotel"},{id:"14",name:"Krankenhaus Porz",type:"medical"},{id:"15",name:"Universität zu Köln",type:"school"},{id:"16",name:"Wohnquartier Ehrenfeld",type:"residential"},{id:"17",name:"Restaurant Himmel un Ääd",type:"restaurant"},{id:"18",name:"Büroturm KölnTriangle",type:"office"},{id:"19",name:"Shopping Arkaden Bonn",type:"retail"},{id:"20",name:"Rheinische Fachhochschule",type:"school"}],z=s=>({hotel:k,office:w,medical:v,residential:g,retail:x,restaurant:f,school:y})[s],S=()=>{const s=u.useRef(null),i=u.useRef(null),p=[...h,...h];return u.useEffect(()=>{const e=s.current;if(!e)return;const a=window.matchMedia("(prefers-reduced-motion: reduce)").matches,n=window.matchMedia("(max-width: 768px)").matches,c=window.matchMedia("(max-width: 480px)").matches;if(a){e.style.animationPlayState="paused";return}n&&(e.style.animationPlayState="running",e.style.willChange="transform",e.style.webkitAnimationPlayState="running",e.style.backfaceVisibility="hidden",e.style.webkitBackfaceVisibility="hidden",e.style.perspective="1000px");const r=new IntersectionObserver(l=>{l.forEach(d=>{d.isIntersecting?a||(e.style.animationPlayState="running",e.style.webkitAnimationPlayState="running",n&&(e.style.backfaceVisibility="hidden",e.style.webkitBackfaceVisibility="hidden",e.style.perspective="1000px",e.style.touchAction="pan-y pinch-zoom")):n||(e.style.animationPlayState="paused")})},{threshold:c?.05:.1,rootMargin:n?"50px":"0px"});r.observe(e);const o=()=>{window.matchMedia("(max-width: 768px)").matches&&!a&&(e.style.animationPlayState="running",e.style.webkitAnimationPlayState="running",e.style.backfaceVisibility="hidden",e.style.webkitBackfaceVisibility="hidden",e.style.perspective="1000px",e.style.touchAction="pan-y pinch-zoom")};return window.addEventListener("resize",o),()=>{r.disconnect(),window.removeEventListener("resize",o)}},[]),t.jsxs("section",{className:"suz-company-showcase overflow-hidden relative suz-section-standard","aria-label":"Unsere Kunden und Partner",role:"region",children:[t.jsxs("div",{className:"text-center mb-16 px-4 animate-fade-in",children:[t.jsxs("h2",{className:"suz-section-title text-slate-100 mb-8",children:["Vertrauen von ",t.jsx("span",{className:"gradient-text",children:"führenden Unternehmen"})]}),t.jsx("p",{className:"suz-text-heading-lg text-slate-300 max-w-3xl mx-auto font-light",children:"Über 500 zufriedene Kunden vertrauen auf unsere professionellen Reinigungsdienstleistungen"})]}),t.jsxs("div",{className:"relative",children:[t.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-black via-black/90 to-transparent z-10 pointer-events-none"}),t.jsx("div",{className:"absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-black via-black/90 to-transparent z-10 pointer-events-none"}),t.jsx("div",{ref:s,className:"suz-company-scroll flex gap-10 animate-scroll-right",role:"list","aria-label":"Kontinuierlich scrollende Liste unserer Kunden","aria-live":"polite","aria-atomic":"false",onTouchStart:e=>{const a=e.touches[0];i.current={x:a.clientX,y:a.clientY,time:Date.now()};const n=e.currentTarget,c=o=>{if(!i.current)return;const l=o.touches[0],d=Math.abs(l.clientX-i.current.x),b=Math.abs(l.clientY-i.current.y);d>b&&d>10&&o.preventDefault()},r=()=>{i.current=null,n.removeEventListener("touchmove",c),n.removeEventListener("touchend",r)};n.addEventListener("touchmove",c,{passive:!1}),n.addEventListener("touchend",r)},children:p.map((e,a)=>{const n=z(e.type);return t.jsx("div",{className:"suz-company-card flex-shrink-0",role:"listitem",tabIndex:0,"aria-label":`Kunde: ${e.name}, Kategorie: ${m(e.type)}`,children:t.jsxs("div",{className:"suz-card-glass glass-morphism-premium rounded-3xl border border-white/30 shadow-2xl hover:shadow-3xl transition-all duration-500 group relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500 pointer-events-none"}),t.jsxs("div",{className:"suz-company-card-content text-center relative z-10",children:[t.jsx("div",{className:"mb-4 flex justify-center",children:t.jsx("div",{className:"icon-badge-enhanced group-hover:scale-110 group-hover:rotate-3 transition-all duration-500",children:t.jsx(n,{size:32,className:"text-blue-400 drop-shadow-lg transition-all duration-500 group-hover:text-blue-300 group-hover:scale-110",strokeWidth:2})})}),t.jsx("h3",{className:"suz-text-body-lg font-semibold text-slate-100 mb-3 group-hover:text-white transition-colors duration-300 suz-german-business-name",children:e.name}),t.jsx("div",{className:"mt-3",children:t.jsx("span",{className:"inline-block px-4 py-2 text-sm font-medium rounded-full bg-gradient-to-r from-blue-900/40 to-blue-800/40 text-blue-300 border border-blue-600/50 backdrop-blur-sm group-hover:from-blue-800/50 group-hover:to-blue-700/50 group-hover:text-blue-200 transition-all duration-300","aria-label":`Unternehmenstyp: ${m(e.type)}`,children:m(e.type)})})]})]})},`${e.id}-${a}`)})})]})]})},m=s=>({hotel:"Hotel",office:"Büro",medical:"Medizin",residential:"Wohnanlage",retail:"Einzelhandel",restaurant:"Gastronomie",school:"Bildung"})[s];export{S as default};
