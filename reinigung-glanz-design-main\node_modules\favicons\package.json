{"name": "favicons", "version": "7.2.0", "description": "Favicon generator for Node.js", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist/"], "repository": {"type": "git", "url": "git+https://github.com/itgalaxy/favicons.git"}, "scripts": {"fmt": "prettier --write .", "fmt-check": "prettier --check .", "lint": "eslint . --ignore-path .gitignore", "fix": "npm run lint -- --fix && npm run fmt", "pretest": "npm run lint && npm run fmt-check", "test-import": "node ./test-packages/test-mjs/index.js", "test-require": "node ./test-packages/test-cjs/index.js", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "test-all": "npm test && npm run test-import && npm run test-require", "clean": "rimraf dist/", "prebuild": "npm run clean", "build": "unbuild", "prepublishOnly": "npm run build && npm run test-all"}, "keywords": ["favicon", "ico", "generator", "node", "realfavicongenerator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/itgalaxy/favicons/issues"}, "homepage": "https://github.com/itgalaxy/favicons", "engines": {"node": ">=14.0.0"}, "dependencies": {"escape-html": "^1.0.3", "sharp": "^0.33.1", "xml2js": "^0.6.1"}, "devDependencies": {"@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-typescript": "^7.18.6", "@types/escape-html": "^1.0.2", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "cross-env": "^7.0.3", "eslint": "^8.26.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^27.1.3", "eslint-plugin-prettier": "^5.0.0", "icojs": "^0.19.3", "jest": "^29.2.1", "jest-image-snapshot": "^6.1.0", "prettier": "^3.0.0", "rimraf": "^5.0.1", "typescript": "^5.0.4", "unbuild": "^2.0.0"}, "jest": {"testTimeout": 180000, "testMatch": ["<rootDir>/test/**/*.test.mjs"], "extensionsToTreatAsEsm": [".ts"], "setupFilesAfterEnv": ["<rootDir>/test/setup.mjs"], "collectCoverage": true, "collectCoverageFrom": ["src/**/*.ts", "src/**/*.js"], "coverageDirectory": "<rootDir>/coverage", "coverageReporters": ["text-summary", "html"]}}