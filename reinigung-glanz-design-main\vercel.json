{"version": 2, "name": "suz-reinigung", "alias": ["suz-reinigung"], "public": true, "github": {"enabled": false}, "build": {"env": {"NODE_ENV": "production"}}, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm ci", "devCommand": "npm run dev", "framework": "vite", "nodeVersion": "18.x", "regions": ["fra1"], "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "trailingSlash": false, "cleanUrls": true, "env": {"VITE_APP_NAME": "SUZ Reinigung", "VITE_APP_DESCRIPTION": "Premium Reinigungsdienstleistungen", "VITE_APP_URL": "https://suz-reinigung.vercel.app", "VITE_GOOGLE_ANALYTICS_ID": "", "VITE_VERCEL_ANALYTICS_ID": ""}}